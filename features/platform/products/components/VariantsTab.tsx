import { useState, useRef, useEffect, use<PERSON>emo, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Plus,
  ChevronDown,
  ChevronUp,
  Trash2,
  AlertCircle,
  Info,
  Check,
  X,
  MoreHorizontal,
  Settings,
  Layers,
  Save,
} from "lucide-react";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { BadgeButton } from "@/components/ui/badge-button";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { Switch } from "@/components/ui/switch";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Label } from "@/components/ui/label";
import { Card, CardHeader, CardDescription, CardContent } from "@/components/ui/card";
import { cn } from "@/lib/utils/cn";
import { Checkbox } from "@/components/ui/checkbox";
import { motion, AnimatePresence } from "framer-motion";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
  TooltipProvider,
} from "@/components/ui/tooltip";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationPrevious,
  PaginationNext,
} from "@/components/ui/pagination";
import { OptionCard } from "./OptionCard";
import { AddOptionPopover } from "./AddOptionPopover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useId } from "react";
import { VariantCard } from "./VariantCard";
import { gql, useMutation, useQuery } from "@apollo/client";
import { useToast } from "@/components/ui/use-toast";
import { VariantDriftCard } from "./VariantDriftCard";

// All the GraphQL queries and mutations
const UPDATE_PRODUCT_OPTION = gql`
  mutation UpdateProductOption($id: ID!, $data: ProductOptionUpdateInput!) {
    updateProductOption(where: { id: $id }, data: $data) {
      id
      title
      productOptionValues {
        id
        value
      }
    }
  }
`;

const CREATE_PRODUCT_OPTION = gql`
  mutation CreateProductOption($data: ProductOptionCreateInput!) {
    createProductOption(data: $data) {
      id
      title
      productOptionValues {
        id
        value
      }
    }
  }
`;

const CREATE_PRODUCT_OPTION_VALUE = gql`
  mutation CreateProductOptionValue($data: ProductOptionValueCreateInput!) {
    createProductOptionValue(data: $data) {
      id
      value
      productOption {
        id
        title
      }
    }
  }
`;

const UPDATE_PRODUCT_OPTION_VALUE = gql`
  mutation UpdateProductOptionValue(
    $id: ID!
    $data: ProductOptionValueUpdateInput!
  ) {
    updateProductOptionValue(where: { id: $id }, data: $data) {
      id
      value
      productOption {
        id
        title
      }
    }
  }
`;

const DELETE_PRODUCT_OPTION_VALUE = gql`
  mutation DeleteProductOptionValue($id: ID!) {
    deleteProductOptionValue(where: { id: $id }) {
      id
    }
  }
`;

const GET_REGIONS = gql`
  query GetRegions {
    regions {
      id
      code
      name
      taxRate
      currency {
        code
        symbol
        symbolNative
        name
      }
    }
  }
`;

const ADD_VARIANT_PRICE = gql`
  mutation AddVariantPrice($id: ID!, $input: ProductVariantUpdateInput!) {
    updateProductVariant(where: { id: $id }, data: $input) {
      id
      prices {
        id
        amount
        compareAmount
        currency {
          code
          symbol
          name
          symbolNative
        }
        region {
          id
          code
          name
          taxRate
        }
      }
    }
  }
`;

const UPDATE_MONEY_AMOUNT = gql`
  mutation UpdateMoneyAmount($id: ID!, $data: MoneyAmountUpdateInput!) {
    updateMoneyAmount(where: { id: $id }, data: $data) {
      id
      amount
      compareAmount
      currency {
        code
      }
      region {
        id
        code
        name
      }
    }
  }
`;

const DELETE_PRODUCT_VARIANT = gql`
  mutation DeleteProductVariant($id: ID!) {
    deleteProductVariant(where: { id: $id }) {
      id
      title
    }
  }
`;

const CREATE_PRODUCT_VARIANT = gql`
  mutation CreateProductVariant($data: ProductVariantCreateInput!) {
    createProductVariant(data: $data) {
      id
      title
      sku
      barcode
      ean
      upc
      material
      productOptionValues {
        id
        value
        productOption {
          id
          title
        }
      }
      prices {
        id
        amount
        compareAmount
        region {
          id
          code
          name
          currency {
            code
            symbol
            symbolNative
          }
        }
      }
    }
  }
`;

const GET_PRODUCT_DETAILS = gql`
  query GetProductDetails($id: ID!) {
    product(where: { id: $id }) {
      id
      title
      productOptions {
        id
        title
        productOptionValues {
          id
          value
        }
      }
      productVariants {
        id
        title
        sku
        barcode
        ean
        upc
        material
        inventoryQuantity
        productOptionValues {
          id
          value
          productOption {
            id
            title
          }
        }
        prices {
          id
          amount
          compareAmount
          region {
            id
            code
            name
            currency {
              code
              symbol
              symbolNative
            }
          }
        }
      }
    }
  }
`;

// Debug utility
const debug = (label: string, data: any) => {
  if (process.env.NODE_ENV === "development") {
    console.log(`[DEBUG: ${label}]`, data);
  }
};

interface VariantsTabProps {
  product: any;
}

export const VariantForm = ({ variant, onChange, onSave, onCancel }: any) => (
  <div className="flex flex-col">
    <div>
      <div className="p-4 border-b">
        <h3 className="font-medium text-sm mb-1">Edit Variant</h3>
        <p className="text-muted-foreground text-xs">
          Update variant properties
        </p>
      </div>
      <div className="space-y-2">
        <div className="grid gap-3 h-72 overflow-y-auto p-4">
          <div>
            <Label className="text-xs">SKU</Label>
            <Input
              value={variant.sku}
              onChange={(e) => onChange({ ...variant, sku: e.target.value })}
              placeholder="SKU-123"
              className="mt-1 h-8 text-sm"
            />
          </div>
          <div>
            <Label className="text-xs">Barcode</Label>
            <Input
              value={variant.barcode}
              onChange={(e) =>
                onChange({ ...variant, barcode: e.target.value })
              }
              placeholder="123456789"
              className="mt-1 h-8 text-sm"
            />
          </div>
          <div>
            <Label className="text-xs">EAN</Label>
            <Input
              value={variant.ean}
              onChange={(e) => onChange({ ...variant, ean: e.target.value })}
              placeholder="1234567890123"
              className="mt-1 h-8 text-sm"
            />
          </div>
          <div>
            <Label className="text-xs">UPC</Label>
            <Input
              value={variant.upc}
              onChange={(e) => onChange({ ...variant, upc: e.target.value })}
              placeholder="123456789012"
              className="mt-1 h-8 text-sm"
            />
          </div>
          <div>
            <Label className="text-xs">Stock</Label>
            <Input
              type="number"
              value={variant.inventoryQuantity || 0}
              onChange={(e) =>
                onChange({ ...variant, inventoryQuantity: parseInt(e.target.value) })
              }
              className="mt-1 h-8 text-sm"
            />
          </div>
          <div>
            <Label className="text-xs">Material</Label>
            <Input
              value={variant.material}
              onChange={(e) =>
                onChange({ ...variant, material: e.target.value })
              }
              placeholder="Cotton, Polyester, etc."
              className="mt-1 h-8 text-sm"
            />
          </div>
          <div className="col-span-full flex items-center space-x-2">
            <Checkbox
              id="manage-inventory"
              checked={variant.manageInventory}
              onCheckedChange={(checked) =>
                onChange({ ...variant, manageInventory: checked })
              }
            />
            <Label htmlFor="manage-inventory" className="text-xs">
              Track quantity
            </Label>
          </div>
          <div className="col-span-full flex items-center space-x-2">
            <Checkbox
              id="allow-backorder"
              checked={variant.allowBackorder}
              onCheckedChange={(checked) =>
                onChange({ ...variant, allowBackorder: checked })
              }
            />
            <Label htmlFor="allow-backorder" className="text-xs">
              Continue selling when out of stock
            </Label>
          </div>
        </div>
      </div>
      <div className="flex justify-end gap-2 p-4 border-t">
        <Button size="sm" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button size="sm" onClick={onSave}>
          Save
        </Button>
      </div>
    </div>
  </div>
);

export function VariantsTab({ product: initialProduct }: VariantsTabProps) {
  // All state hooks first
  const [activeTab, setActiveTab] = useState("options");
  const [selectedFilters, setSelectedFilters] = useState<string[]>([]);
  const [isVariantsExpanded, setIsVariantsExpanded] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [variantsToCreate, setVariantsToCreate] = useState<any[]>([]);
  const [variantsToDelete, setVariantsToDelete] = useState<any[]>([]);
  const [unchangedVariants, setUnchangedVariants] = useState<any[]>([]);
  const [options, setOptions] = useState<any[]>([]);
  
  // All queries and mutations
  const { data: productData, loading: productLoading } = useQuery(GET_PRODUCT_DETAILS, {
    variables: { id: initialProduct?.id },
    skip: !initialProduct?.id,
  });
  
  const { refetch } = useQuery(
    gql`
      query GetProduct($id: ID!) {
        product(where: { id: $id }) {
          id
          productVariants {
            id
            title
            sku
            barcode
            ean
            upc
            material
            productOptionValues {
              id
              value
              productOption {
                id
                title
              }
            }
            prices {
              id
              amount
              compareAmount
              region {
                id
                code
                name
                currency {
                  code
                  symbol
                  symbolNative
                }
              }
            }
          }
        }
      }
    `,
    {
      variables: { id: initialProduct?.id },
      skip: true,
    }
  );

  const { toast } = useToast();
  const [updateProductOption] = useMutation(UPDATE_PRODUCT_OPTION);
  const [createProductOption] = useMutation(CREATE_PRODUCT_OPTION);
  const [createProductOptionValue] = useMutation(CREATE_PRODUCT_OPTION_VALUE);
  const [updateProductOptionValue] = useMutation(UPDATE_PRODUCT_OPTION_VALUE);
  const [updateVariantPrice] = useMutation(ADD_VARIANT_PRICE);
  const [updateMoneyAmount] = useMutation(UPDATE_MONEY_AMOUNT);
  const [deleteProductOptionValue] = useMutation(DELETE_PRODUCT_OPTION_VALUE);
  const [createProductVariant] = useMutation(CREATE_PRODUCT_VARIANT);
  const [deleteProductVariant] = useMutation(DELETE_PRODUCT_VARIANT);
  const { data: regionsData } = useQuery(GET_REGIONS);

  // Derived values
  const product = productData?.product || initialProduct;
  const regions = useMemo(() => {
    if (!regionsData?.regions) return [];
    return regionsData.regions.map((region: any) => ({
      ...region,
      currencyCode: region.currency?.code,
      currencySymbol: region.currency?.symbolNative,
      locale:
        region.currency?.code === "USD"
          ? "en-US"
          : region.currency?.code === "EUR"
            ? "de-DE"
            : region.currency?.code === "GBP"
              ? "en-GB"
              : "en-US",
    }));
  }, [regionsData]);

  // useRef for recursive function
  const generateCombinationsRef = useRef<any>(null);

  // Initialize options and variants when product data changes
  useEffect(() => {
    if (product?.productOptions) {
      setOptions(
        product.productOptions.map((option: any) => ({
          id: option?.id || `temp-${Date.now()}-${Math.random()}`,
          title: option?.title || '',
          values: (option?.productOptionValues || []).map((value: any) => ({
            value: value?.value || '',
            label: value?.value || '',
            id: value?.id || `temp-${Date.now()}-${Math.random()}`,
          })),
        }))
      );
    }
    
    if (product?.productVariants) {
      setUnchangedVariants(product.productVariants);
    }
  }, [product]);

  // Setup generateCombinations function
  useEffect(() => {
    generateCombinationsRef.current = (arrays: any[]): any[] => {
      if (arrays.length === 0) return [[]];
      const result: any[] = [];
      const restCombinations = generateCombinationsRef.current(arrays.slice(1));
      arrays[0].forEach((item: any) => {
        restCombinations.forEach((combination: any) => {
          result.push([item, ...combination]);
        });
      });
      return result;
    };
  }, []);

  // All callback functions
  const normalizeOptionValues = useCallback((optionValues: any[]) => {
    return [...optionValues].sort((a, b) => {
      const aKey = a.productOption?.title || a.option;
      const bKey = b.productOption?.title || b.option;
      return aKey.localeCompare(bKey);
    });
  }, []);

  const optionValuesMatch = useCallback((existing: any, candidate: any) => {
    if (!existing || !candidate) return false;
    const normalizedExisting = normalizeOptionValues(existing);
    const normalizedCandidate = normalizeOptionValues(candidate);
    if (normalizedExisting.length !== normalizedCandidate.length) return false;
    return normalizedExisting.every((existingOv: any, index: number) => {
      const candidateOv = normalizedCandidate[index];
      const existingOption =
        existingOv.productOption?.title || existingOv.option;
      const candidateOption =
        candidateOv.productOption?.title || candidateOv.option;
      return (
        existingOption === candidateOption &&
        existingOv.value === candidateOv.value
      );
    });
  }, [normalizeOptionValues]);

  const findSimilarVariant = useCallback((optionValues: any[], existingVariants: any[]) => {
    const sortedNewValues = [...optionValues].sort((a, b) =>
      a.option.localeCompare(b.option)
    );

    let bestMatch = null;
    let maxMatchScore = -1;

    existingVariants.forEach((variant) => {
      if (!variant?.productOptionValues) return;
      const sortedExistingValues = [...variant.productOptionValues].sort((a, b) =>
        (a?.productOption?.title || '').localeCompare(b?.productOption?.title || '')
      );

      let matchScore = 0;
      let exactMatches = 0;

      sortedNewValues.forEach((newValue) => {
        const matchingValue = sortedExistingValues.find(
          (existingValue) => existingValue?.productOption?.title === newValue.option
        );

        if (matchingValue) {
          if (matchingValue.value === newValue.value) {
            matchScore += 100;
            exactMatches++;
          }
        }
      });

      matchScore += exactMatches * 50;

      if (matchScore > maxMatchScore) {
        maxMatchScore = matchScore;
        bestMatch = variant;
      }
    });

    return bestMatch;
  }, []);

  const calculateVariantDrift = useCallback((currentOptions: any[]) => {
    debug("Calculating variant drift", {
      currentOptions,
      existingVariants: product.productVariants || [],
    });

    const optionValues = currentOptions.map((option) =>
      option.values.map((v: any) => ({
        option: option.title,
        value: v.value,
        label: v.label,
        optionId: option.id,
        valueId: v.id,
      }))
    );

    const combinations = generateCombinationsRef.current(optionValues);
    debug("Generated combinations", combinations);

    const newVariants = combinations
      .map((combination: any) => {
        const optionValuePairs = combination.map((value: any) => ({
          option: value.option,
          value: value.value,
          label: value.label,
          optionId: value.optionId,
          valueId: value.valueId,
        }));

        const exists = (product.productVariants || []).some((variant: any) => {
          if (!variant?.productOptionValues) return false;
          const variantOptionValues = variant.productOptionValues.map((ov: any) => ({
            option: ov?.productOption?.title || '',
            value: ov?.value || '',
            label: ov?.value || '',
          }));
          return optionValuesMatch(variantOptionValues, optionValuePairs);
        });

        if (exists) return null;

        const similarVariant = findSimilarVariant(
          optionValuePairs,
          product.productVariants || []
        );
        const copiedPrices =
          similarVariant?.prices?.map((price: any) => ({
            id: `new-price-${Date.now()}-${Math.random()}`,
            currency: {
              code: price.region.currency.code,
              symbol: price.region.currency.symbol,
              symbolNative: price.region.currency.symbolNative,
              name: price.region.currency.name,
            },
            amount: price.amount,
            compareAmount: price.compareAmount,
            region: price.region,
          })) || [];

        return {
          id: `new-${Date.now()}-${Math.random()}`,
          title: optionValuePairs.map((ov: any) => ov.value).join(" / "),
          sku: "",
          barcode: "",
          ean: "",
          upc: "",
          inventoryQuantity: 100,
          manageInventory: false,
          allowBackorder: false,
          hsCode: "",
          originCountry: "",
          midCode: "",
          material: "",
          prices: copiedPrices,
          productOptionValues: optionValuePairs.map((ov: any) => ({
            productOption: {
              id: ov.optionId,
              title: ov.option,
            },
            value: ov.value,
            id: ov.valueId,
          })),
        };
      })
      .filter(Boolean);

    const variantsToRemove = (product.productVariants || []).filter((variant: any) => {
      if (!variant?.productOptionValues) return false;
      
      const variantOptionValues = variant.productOptionValues.map((ov: any) => ({
        option: ov?.productOption?.title || '',
        value: ov?.value || '',
        label: ov?.value || '',
      }));

      return !combinations.some((combination: any) => {
        const optionValuePairs = combination.map((value: any) => ({
          option: value.option,
          value: value.value,
          label: value.label,
        }));
        return optionValuesMatch(variantOptionValues, optionValuePairs);
      });
    });

    const unchangedVariants = (product.productVariants || []).filter((variant: any) => {
      return !variantsToRemove.includes(variant);
    });

    debug("Variant drift calculation results", {
      toCreate: newVariants,
      toDelete: variantsToRemove,
      unchanged: unchangedVariants,
    });

    setVariantsToCreate(newVariants);
    setVariantsToDelete(variantsToRemove);
    setUnchangedVariants(unchangedVariants);
  }, [product.productVariants, optionValuesMatch, findSimilarVariant]);

  // Calculate variant drift whenever options change
  useEffect(() => {
    calculateVariantDrift(options);
  }, [options, calculateVariantDrift]);

  // Early return after all hooks
  if (!product || productLoading) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        Loading product data...
      </div>
    );
  }

  // Handler functions
  const handleOptionFilter = (option: string, value: string) => {
    const filter = `${option}:${value}`;
    setSelectedFilters((prev) =>
      prev.includes(filter)
        ? prev.filter((f) => f !== filter)
        : [...prev, filter]
    );
  };

  const getFilteredVariants = (variants: any[]) => {
    if (!selectedFilters.length || !variants) return variants || [];

    return variants.filter((variant) =>
      selectedFilters.every((filter) => {
        if (!variant?.productOptionValues) return false;
        const [option, value] = filter.split(":");
        return variant.productOptionValues.some(
          (ov: any) => ov?.productOption?.title === option && ov?.value === value
        );
      })
    );
  };

  const handleAddOption = async (newOption: any) => {
    try {
      const result = await createProductOption({
        variables: {
          data: {
            title: newOption.title,
            product: {
              connect: { id: product.id },
            },
          },
        },
      });

      if (result.data?.createProductOption) {
        const createdOption = result.data.createProductOption;

        setOptions((prev) => [
          ...prev,
          {
            id: createdOption.id,
            title: createdOption.title,
            values: [],
          },
        ]);

        toast({
          title: "Option created successfully",
          variant: "default",
        });

        calculateVariantDrift([
          ...options,
          {
            id: createdOption.id,
            title: createdOption.title,
            values: [],
          },
        ]);
      }
    } catch (error: any) {
      console.error("Error creating option:", error);
      toast({
        title: "Error creating option",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  const handleOptionUpdate = async (updatedOption: any) => {
    try {
      const currentOption = options.find((o) => o.id === updatedOption.id);
      const currentValues = currentOption?.values || [];

      const newValues = updatedOption.values.filter((v: any) => !v.id);
      const deletedValues = currentValues.filter(
        (cv: any) => !updatedOption.values.some((uv: any) => uv.id === cv.id)
      );

      for (const value of deletedValues) {
        await deleteProductOptionValue({
          variables: {
            id: value.id,
          },
        });
      }

      for (const value of newValues) {
        const result = await createProductOptionValue({
          variables: {
            data: {
              value: value.value,
              productOption: {
                connect: { id: updatedOption.id },
              },
            },
          },
        });

        if (result.data?.createProductOptionValue) {
          const createdValue = result.data.createProductOptionValue;
          value.id = createdValue.id;
        }
      }

      const updatedValues = updatedOption.values
        .filter(
          (v: any) =>
            !deletedValues.some((dv: any) => dv.id === v.id) ||
            newValues.some((nv: any) => nv.value === v.value)
        )
        .map((v: any) => ({
          id: v.id,
          value: v.value,
          label: v.value,
        }));

      setOptions((prev) =>
        prev.map((opt) =>
          opt.id === updatedOption.id
            ? {
                ...opt,
                title: updatedOption.title,
                values: updatedValues,
              }
            : opt
        )
      );

      if (
        updatedOption.title !==
        options.find((o) => o.id === updatedOption.id)?.title
      ) {
        await updateProductOption({
          variables: {
            id: updatedOption.id,
            data: {
              title: updatedOption.title,
            },
          },
        });
      }

      toast({
        title: "Option updated successfully",
        variant: "default",
      });

      calculateVariantDrift(
        options.map((opt) =>
          opt.id === updatedOption.id
            ? {
                ...opt,
                title: updatedOption.title,
                values: updatedValues,
              }
            : opt
        )
      );
    } catch (error: any) {
      console.error("Error updating option:", error);
      toast({
        title: "Error updating option",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  const handleRemoveFromCreate = (variant: any) => {
    setVariantsToCreate((prev) => prev.filter((v) => v.id !== variant.id));
  };

  const handleRemoveFromDelete = (variant: any) => {
    setVariantsToDelete((prev) => prev.filter((v) => v.id !== variant.id));
    setUnchangedVariants((prev) => [...prev, variant]);
  };

  const handleAddPrice = async (variant: any, priceData: any) => {
    try {
      debug("Adding/Updating price - Initial state", {
        variant,
        priceData,
        currentVariants: product.productVariants || [],
        variantsToCreate,
      });

      if (String(variant.id).startsWith("new-")) {
        debug("Updating price for pending variant:", variant.id);

        const newPrice = {
          id: `new-price-${Date.now()}`,
          amount: priceData.amount,
          compareAmount: priceData.compareAmount,
          currency: {
            code: priceData.currencyCode,
            ...(regions.find((r: any) => r.currency.code === priceData.currencyCode)
              ?.currency || {}),
          },
          region: {
            code: priceData.regionCode,
            ...(regions.find((r: any) => r.code === priceData.regionCode) || {}),
          },
        };

        debug("New price object:", newPrice);

        const updatedVariantsToCreate = variantsToCreate.map((v) => {
          if (v.id === variant.id) {
            const existingPriceIndex = (v.prices || []).findIndex(
              (p: any) => p.region?.code === priceData.regionCode
            );

            let updatedPrices;
            if (existingPriceIndex >= 0) {
              updatedPrices = [...(v.prices || [])];
              updatedPrices[existingPriceIndex] = newPrice;
            } else {
              updatedPrices = [...(v.prices || []), newPrice];
            }

            return { ...v, prices: updatedPrices };
          }
          return v;
        });

        debug("Updated variantsToCreate state:", updatedVariantsToCreate);
        setVariantsToCreate(updatedVariantsToCreate);

        toast({
          title: "Price updated for new variant",
          variant: "default",
        });
        return;
      }

      if (priceData.priceId) {
        await updateMoneyAmount({
          variables: {
            id: priceData.priceId,
            data: {
              amount: priceData.amount,
              compareAmount: priceData.compareAmount,
            },
          },
        });
      } else {
        await updateVariantPrice({
          variables: {
            id: variant.id,
            input: {
              prices: {
                create: [
                  {
                    amount: priceData.amount,
                    compareAmount: priceData.compareAmount,
                    currency: {
                      connect: { code: priceData.currencyCode.toLowerCase() },
                    },
                    ...(priceData.regionCode
                      ? {
                          region: {
                            connect: { code: priceData.regionCode },
                          },
                        }
                      : {}),
                  },
                ],
              },
            },
          },
        });
      }

      toast({
        title: priceData.priceId ? "Price updated" : "Price added",
        variant: "default",
      });
    } catch (error: any) {
      console.error("Error handling price:", error);
      toast({
        title: "Error updating price",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  const handleSaveChanges = async (localVariants: any[]) => {
    setIsSaving(true);
    const errors: string[] = [];

    try {
      for (const variant of localVariants) {
        try {
          const optionValueIds = variant.productOptionValues.map((ov: any) => ov.id);
          const prices =
            variant.prices?.map((price: any) => ({
              amount: price.amount,
              compareAmount: price.compareAmount,
              currency: {
                connect: { code: price.currency.code.toLowerCase() },
              },
              region: { connect: { code: price.region.code } },
            })) || [];

          await createProductVariant({
            variables: {
              data: {
                title: variant.title,
                sku: variant.sku || "",
                barcode: variant.barcode || "",
                ean: variant.ean || "",
                upc: variant.upc || "",
                material: variant.material || "",
                inventoryQuantity: variant.inventoryQuantity || 100,
                manageInventory: variant.manageInventory || false,
                allowBackorder: variant.allowBackorder || false,
                hsCode: variant.hsCode || "",
                originCountry: variant.originCountry || "",
                midCode: variant.midCode || "",
                product: { connect: { id: product.id } },
                productOptionValues: {
                  connect: optionValueIds.map((id: string) => ({ id })),
                },
                prices: { create: prices },
              },
            },
          });

          toast({
            title: "Variant Created",
            description: `Successfully created variant: ${variant.title}`,
          });
        } catch (error: any) {
          errors.push(
            `Failed to create variant ${variant.title}: ${error.message}`
          );
        }
      }

      for (const variant of variantsToDelete) {
        try {
          await deleteProductVariant({
            variables: {
              id: variant.id,
            },
          });

          toast({
            title: "Variant Deleted",
            description: `Successfully deleted variant: ${variant.title}`,
          });
        } catch (error: any) {
          errors.push(
            `Failed to delete variant ${variant.title}: ${error.message}`
          );
        }
      }

      if (errors.length > 0) {
        toast({
          variant: "destructive",
          title: "Some operations failed",
          description: "Check the console for details",
        });
        console.error("Variant operations errors:", errors);
      } else {
        toast({
          title: "Changes Saved",
          description: "All variant changes have been applied successfully",
        });

        setVariantsToCreate([]);
        setVariantsToDelete([]);
        await refetch();
      }
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to save changes. Please try again.",
      });
      console.error("Save changes error:", error);
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="space-y-6">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="h-auto mx-auto flex w-full bg-transparent max-w-xl">
          <TabsTrigger
            value="options"
            className="group data-[state=active]:bg-muted flex-1 flex-col p-3 text-xs data-[state=active]:shadow-none"
          >
            <Badge
              className="mb-1.5 border rounded-full transition-opacity group-data-[state=inactive]:opacity-50"
            >
              {options.length}
            </Badge>
            Options
          </TabsTrigger>
          <TabsTrigger
            value="variants"
            className="group data-[state=active]:bg-muted flex-1 flex-col p-3 text-xs data-[state=active]:shadow-none"
          >
            <Badge
              className="mb-1.5 border rounded-full transition-opacity group-data-[state=inactive]:opacity-50"
            >
              {(product.productVariants || []).length}
            </Badge>
            Variants
          </TabsTrigger>
          <TabsTrigger
            value="drift"
            className="group data-[state=active]:bg-muted flex-1 flex-col p-3 text-xs data-[state=active]:shadow-none"
          >
            <div className="flex gap-1 mb-1.5">
              {variantsToCreate.length > 0 && (
                <Badge
                  color="emerald"
                  className="border rounded-full transition-opacity group-data-[state=inactive]:opacity-50"
                >
                  {variantsToCreate.length}
                </Badge>
              )}
              {variantsToDelete.length > 0 && (
                <Badge
                  color="rose"
                  className="border rounded-full transition-opacity group-data-[state=inactive]:opacity-50"
                >
                  {variantsToDelete.length}
                </Badge>
              )}
              {unchangedVariants.length > 0 && (
                <Badge
                  className="border rounded-full transition-opacity group-data-[state=inactive]:opacity-50"
                >
                  {unchangedVariants.length}
                </Badge>
              )}
            </div>
            Variant Drift
          </TabsTrigger>
        </TabsList>

        <TabsContent value="options" className="mt-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-lg font-semibold">Product Options</h2>
              <div className="text-sm text-muted-foreground">
                Add options like different sizes, colors, and materials
              </div>
            </div>
            <AddOptionPopover onAdd={handleAddOption} />
          </div>

          <div className="space-y-4 mt-6">
            {options.map((option) => (
              <OptionCard
                key={option.id}
                option={option}
                onUpdate={handleOptionUpdate}
              />
            ))}
          </div>
        </TabsContent>

        <TabsContent value="variants" className="mt-6">
          <div className="space-y-4">
            <div
              className="flex items-center justify-between cursor-pointer"
              onClick={() => setIsVariantsExpanded(!isVariantsExpanded)}
            >
              <h3 className="font-medium uppercase text-xs tracking-wider text-muted-foreground">
                Current Variants
              </h3>
              <Badge className="py-0 text-[11px] border uppercase font-medium tracking-wide rounded-full flex items-center gap-1">
                {(product.productVariants || []).length}
                <ChevronDown
                  className={cn(
                    "h-3 w-3 transition-transform",
                    !isVariantsExpanded && "rotate-180"
                  )}
                />
              </Badge>
            </div>
            {selectedFilters.length > 0 && (
              <div className="flex flex-wrap gap-2 mt-2">
                {selectedFilters.map((filter) => {
                  const [option, value] = filter.split(":");
                  return (
                    <Badge
                      key={filter}
                      color="zinc"
                      className={cn(
                        " flex items-center gap-1 uppercase rounded-md border text-[.65rem] py-[.0625rem] px-1.5 font-medium cursor-pointer hover:bg-accent/50"
                      )}
                    >
                      <span className="opacity-80">{option}:</span>
                      {value}
                      <X
                        className="h-3 w-3 ml-1 cursor-pointer"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleOptionFilter(option, value);
                        }}
                      />
                    </Badge>
                  );
                })}
              </div>
            )}
            {isVariantsExpanded && (
              <>
                {getFilteredVariants(product.productVariants || []).map((variant: any) => (
                  <VariantCard
                    key={variant.id}
                    variant={variant}
                    showActions={true}
                    regions={regions}
                    onAddPrice={handleAddPrice}
                    selectedFilters={selectedFilters}
                    onOptionFilter={handleOptionFilter}
                  />
                ))}
                {selectedFilters.length > 0 &&
                  getFilteredVariants(product.productVariants || []).length === 0 && (
                    <div className="text-center py-8 text-muted-foreground">
                      No variants match the selected filters
                    </div>
                  )}
              </>
            )}
          </div>
        </TabsContent>

        <TabsContent value="drift" className="mt-6">
          {variantsToCreate.length > 0 || variantsToDelete.length > 0 ? (
            <VariantDriftCard
              variantsToCreate={variantsToCreate}
              variantsToDelete={variantsToDelete}
              unchangedVariants={unchangedVariants}
              onRemoveFromCreate={handleRemoveFromCreate}
              onRemoveFromDelete={handleRemoveFromDelete}
              onSaveChanges={handleSaveChanges}
              regions={regions}
            >
              <Button
                onClick={() => handleSaveChanges(variantsToCreate)}
                disabled={isSaving}
                className="w-full"
              >
                {isSaving ? (
                  <div className="flex items-center gap-2">
                    <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                    Saving Changes...
                  </div>
                ) : (
                  "Save Changes"
                )}
              </Button>
            </VariantDriftCard>
          ) : (
            <Card>
              <CardContent className="pt-6">
                <div className="flex flex-col items-center justify-center text-center py-6">
                  <Layers className="h-12 w-12 text-muted-foreground/50" />
                  <h3 className="mt-4 text-lg font-semibold">
                    No Variant Changes
                  </h3>
                  <p className="mt-2 text-sm text-muted-foreground">
                    All variants are in sync with your current options. Add or
                    modify options to see variant changes here.
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}