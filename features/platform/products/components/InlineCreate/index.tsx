"use client";

import { useState, useCallback } from "react";
import isDeepEqual from "fast-deep-equal";
import { useFieldsObj } from "@/features/dashboard/lib/useFieldObj";
import { gql, useMutation } from "@apollo/client";
import {
  makeDataGetter,
  useInvalidFields,
  serializeValueToObjByFieldKey,
} from "@/features/dashboard/lib/dataGetter";
import { Button } from "@/components/ui/button";
import { GraphQLErrorNotice } from "@/features/dashboard/components/GraphQLErrorNotice";
import { useToast } from "@/components/ui/use-toast";
import { CustomFields } from "../CustomFields";
import * as ImageView from "../views/Image";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Plus } from "lucide-react";
import { cn } from "@/lib/utils/cn";
import type { DataGetter } from "@/features/dashboard/lib/dataGetter";

interface InlineCreateProps {
  list: any;
  onCancel: () => void;
  onCreate: (itemGetter: DataGetter<any>) => void;
  fields: string[];
  selectedFields: string;
}

export function InlineCreate({
  list,
  onCancel,
  onCreate,
  fields: fieldPaths,
  selectedFields,
}: InlineCreateProps) {
  const { toast } = useToast();
  const fields = useFieldsObj(list, fieldPaths);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);

  // Define custom views for specific fields
  const fieldViews = {
    image: {
      ...ImageView,
      Field: ({ autoFocus, field, value, onChange }: any) => (
        <ImageView.Field
          autoFocus={autoFocus}
          field={field}
          value={value}
          onChange={onChange}
        />
      ),
    },
  };

  const [createItem, { loading, error }] = useMutation(
    gql`mutation($data: ${list.gqlNames.createInputName}!) {
      item: ${list.gqlNames.createMutationName}(data: $data) {
        ${selectedFields}
    }
  }`
  );

  const [value, setValue] = useState(() => {
    const value: Record<string, any> = {};
    Object.keys(fields).forEach((fieldPath) => {
      value[fieldPath] = { kind: "value", value: fields[fieldPath].controller.defaultValue };
    });
    return value;
  });

  const invalidFields = useInvalidFields(fields, value);
  const [forceValidation, setForceValidation] = useState(false);

  const onCreateClick = useCallback(async () => {
    const hasValidationErrors = !!invalidFields.size;
    if (hasValidationErrors) {
      setForceValidation(true);
      return;
    }

    const data: Record<string, any> = {};
    const allSerializedValues = serializeValueToObjByFieldKey(fields, value);
    
    Object.keys(allSerializedValues).forEach((fieldPath) => {
      const { controller } = fields[fieldPath];
      const serialized = allSerializedValues[fieldPath];
      if (!isDeepEqual(serialized, controller.serialize(controller.defaultValue))) {
        Object.assign(data, serialized);
      }
    });

    try {
      const result = await createItem({ variables: { data } });
      if (result.data) {
        const itemGetter = makeDataGetter(result.data, result.errors).get("item");
        toast({
          title: "Success",
          description: `Created ${list.singular}`,
        });
        setCreateDialogOpen(false);
        onCreate(itemGetter);
      }
    } catch (err) {
      // Error is handled by the error prop
    }
  }, [createItem, fields, invalidFields, list.singular, onCreate, toast, value]);

  // Simple add button view
  return (
    <>
      <button
        onClick={() => setCreateDialogOpen(true)}
        className="w-full h-full rounded-md border-2 border-dashed border-muted-foreground/30 hover:border-muted-foreground/50 transition-colors flex items-center justify-center group cursor-pointer min-h-[200px]"
      >
        <div className="text-center">
          <Plus className="w-8 h-8 mx-auto mb-2 text-muted-foreground/50 group-hover:text-muted-foreground/70 transition-colors" />
          <p className="text-sm text-muted-foreground/70 group-hover:text-muted-foreground transition-colors">
            Add {list.singular}
          </p>
        </div>
      </button>

      <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Create {list.singular}</DialogTitle>
          </DialogHeader>
          
          <div className="space-y-4">
            <CustomFields
              fields={fields}
              onChange={useCallback(
                (getNewValue: (value: any) => any) => {
                  setValue((value) => getNewValue(value));
                },
                []
              )}
              value={value}
              forceValidation={forceValidation}
              invalidFields={invalidFields}
              fieldViews={fieldViews}
            />
          </div>

          <div className="flex gap-2 justify-end">
            <Button
              variant="outline"
              onClick={() => {
                setCreateDialogOpen(false);
                onCancel();
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={onCreateClick}
              disabled={loading}
            >
              {loading ? "Creating..." : `Create ${list.singular}`}
            </Button>
          </div>

          {error && (
            <GraphQLErrorNotice
              errors={error?.graphQLErrors}
              networkError={error?.networkError}
            />
          )}
        </DialogContent>
      </Dialog>
    </>
  );
}