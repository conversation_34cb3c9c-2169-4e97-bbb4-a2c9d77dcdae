"use client";

import { Fragment, useEffect, useRef, useState, type FC } from "react";
import { forwardRefWithAs } from "@/features/dashboard/lib/forwardRefWithAs";
import { useItemState } from "@/features/dashboard/lib/useItemState";
import { gql, useApolloClient } from "@apollo/client";
import { useKeystone } from "@/features/dashboard/components/providers/KeystoneProvider";
import { useList } from "@/features/dashboard/hooks/useAdminMeta";
import {
  getRootGraphQLFieldsFromFieldController,
  makeDataGetter,
} from "@/features/dashboard/lib/dataGetter";
import { cn } from "@/lib/utils/cn";
import { Loader2 } from "lucide-react";
import { InlineEdit } from "../InlineEdit";
import { InlineCreate } from "../InlineCreate";
import { RelationshipSelect } from "@/features/dashboard/components/RelationshipSelect";
import { Button } from "@/components/ui/button";
import { FieldContainer } from "@/components/ui/field-container";
import { FieldLabel } from "@/components/ui/field-label";
import { FieldLegend } from "@/components/ui/field-label";
import { FieldDescription } from "@/components/ui/field-description";
import { CreateItemDrawer } from "@/features/dashboard/components/CreateItemDrawer";
import type { FieldMeta } from "@/features/dashboard/types";

interface CardContainerProps {
  mode?: "view" | "create" | "edit";
  children?: React.ReactNode;
  className?: string;
}

const CardContainer = forwardRefWithAs<HTMLDivElement, CardContainerProps>(
  ({ mode = "view", className, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          `relative before:content-[' '] before:rounded-xl before:w-1 before:h-full before:absolute before:left-0 before:top-0 before:bottom-0 before:z-10`,
          className
        )}
        {...props}
      />
    );
  }
);

interface DisplayOptions {
  cardFields: string[];
  inlineEdit?: {
    fields: string[];
  };
  inlineCreate?: {
    fields: string[];
  };
  linkToItem?: boolean;
  removeMode?: string;
  inlineConnect?: boolean;
}

interface CardsValue {
  displayOptions: DisplayOptions;
  currentIds: Set<string>;
  initialIds: Set<string>;
  itemBeingCreated: boolean;
  itemsBeingEdited: Set<string>;
  id: string | null;
}

interface CardsProps {
  localList: any;
  field: any;
  foreignList: any;
  id: string;
  value: CardsValue;
  onChange?: (value: CardsValue) => void;
  forceValidation?: boolean;
}

export function Cards({
  localList,
  field,
  foreignList,
  id,
  value,
  onChange,
  forceValidation,
}: CardsProps) {
  const { displayOptions } = value;
  let selectedFields = [
    ...new Set([
      ...displayOptions.cardFields,
      ...(displayOptions.inlineEdit?.fields || []),
    ]),
  ]
    .map((fieldPath) => {
      return foreignList.fields[fieldPath].controller.graphqlSelection;
    })
    .join("\n");
  if (!displayOptions.cardFields.includes("id")) {
    selectedFields += "\nid";
  }
  if (
    !displayOptions.cardFields.includes(foreignList.labelField) &&
    foreignList.labelField !== "id"
  ) {
    selectedFields += `\n${foreignList.labelField}`;
  }

  const {
    items,
    setItems,
    state: itemsState,
  } = useItemState({
    selectedFields,
    localList,
    id,
    field,
  });

  const client = useApolloClient();

  const [isLoadingLazyItems, setIsLoadingLazyItems] = useState(false);
  const [showConnectItems, setShowConnectItems] = useState(false);
  const [hideConnectItemsLabel, setHideConnectItemsLabel] = useState("Cancel");
  const editRef = useRef<HTMLDivElement>(null);

  const isMountedRef = useRef(false);
  useEffect(() => {
    isMountedRef.current = true;
    return () => {
      isMountedRef.current = false;
    };
  });

  if (itemsState.kind === "loading") {
    return null;
  }
  if (itemsState.kind === "error") {
    return (
      <span className="text-red-600 dark:text-red-500 text-sm">
        {itemsState.message}
      </span>
    );
  }

  const currentIdsArrayWithFetchedItems = [...value.currentIds]
    .map((id) => ({ itemGetter: items[id], id }))
    .filter((x) => x.itemGetter);

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
        {/* Inline Create */}
        <div>
          <CardContainer mode="create">
            <InlineCreate
              selectedFields={selectedFields}
              fields={displayOptions.inlineCreate?.fields || []}
              list={foreignList}
              onCancel={() => {
                onChange?.({ ...value, itemBeingCreated: false });
              }}
              onCreate={(itemGetter: any) => {
                const id = itemGetter.data.id;
                setItems({ ...items, [id]: itemGetter });
                onChange?.({
                  ...value,
                  itemBeingCreated: false,
                  currentIds: field.many
                    ? new Set([...value.currentIds, id])
                    : new Set([id]),
                });
                setTimeout(() => {
                  onChange?.({
                    ...value,
                    itemBeingCreated: true,
                    currentIds: field.many
                      ? new Set([...value.currentIds, id])
                      : new Set([id]),
                  });
                }, 0);
              }}
            />
          </CardContainer>
        </div>

        {/* Inline Edit */}
        {currentIdsArrayWithFetchedItems.map(({ id, itemGetter }, index) => (
          <div key={id}>
            <CardContainer mode="edit">
              <InlineEdit
                list={foreignList}
                fields={displayOptions.inlineEdit?.fields || []}
                onSave={(newItemGetter: any) => {
                  setItems({
                    ...items,
                    [id]: newItemGetter,
                  });
                }}
                selectedFields={selectedFields}
                itemGetter={itemGetter}
                onCancel={() => {}}
              />
            </CardContainer>
          </div>
        ))}
      </div>

      {/* Connect Items Section */}
      {onChange === undefined ? null : displayOptions.inlineConnect &&
        showConnectItems ? (
        <CardContainer mode="edit">
          <div className="flex gap-1 flex-col flex-wrap w-full">
            <RelationshipSelect
              autoFocus
              controlShouldRenderValue={isLoadingLazyItems}
              isDisabled={onChange === undefined}
              list={foreignList}
              labelField={field.refLabelField}
              searchFields={field.refSearchFields}
              isLoading={isLoadingLazyItems}
              placeholder={`Select a ${foreignList.singular}`}
              portalMenu
              state={{
                kind: "many",
                async onChange(options: any[]) {
                  const itemsToFetchAndConnect: string[] = [];
                  options.forEach((item) => {
                    if (!value.currentIds.has(item.id)) {
                      itemsToFetchAndConnect.push(item.id);
                    }
                  });
                  if (itemsToFetchAndConnect.length) {
                    try {
                      const { data, errors } = await client.query({
                        query: gql`query ($ids: [ID!]!) {
                      items: ${foreignList.gqlNames.listQueryName}(where: { id: { in: $ids }}) {
                        ${selectedFields}
                      }
                    }`,
                        variables: { ids: itemsToFetchAndConnect },
                      });
                      if (isMountedRef.current) {
                        const dataGetters = makeDataGetter(data, errors);
                        const itemsDataGetter = dataGetters.get("items");
                        let newItems = { ...items };
                        let newCurrentIds = field.many
                          ? new Set(value.currentIds)
                          : new Set<string>();
                        if (Array.isArray(itemsDataGetter.data)) {
                          itemsDataGetter.data.forEach((item: any, i: number) => {
                            if (item?.id != null) {
                              newCurrentIds.add(item.id);
                              newItems[item.id] = itemsDataGetter.get(i);
                            }
                          });
                        }
                        if (newCurrentIds.size) {
                          setItems(newItems);
                          onChange({
                            ...value,
                            currentIds: newCurrentIds,
                            itemsBeingEdited: new Set(),
                            itemBeingCreated: false
                          });
                          setHideConnectItemsLabel("Done");
                        }
                      }
                    } finally {
                      if (isMountedRef.current) {
                        setIsLoadingLazyItems(false);
                      }
                    }
                  }
                },
                value: (() => {
                  let options: any[] = [];
                  Object.keys(items).forEach((id) => {
                    if (value.currentIds.has(id)) {
                      options.push({ id, label: id });
                    }
                  });
                  return options;
                })(),
              }}
            />
            <div>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => setShowConnectItems(false)}
              >
                {hideConnectItemsLabel}
              </Button>
            </div>
          </div>
        </CardContainer>
      ) : null}
      {forceValidation && (
        <span className="text-red-600 dark:text-red-500 text-sm">
          You must finish creating and editing any related{" "}
          {foreignList.label.toLowerCase()} before saving the{" "}
          {localList.singular.toLowerCase()}
        </span>
      )}
    </div>
  );
}