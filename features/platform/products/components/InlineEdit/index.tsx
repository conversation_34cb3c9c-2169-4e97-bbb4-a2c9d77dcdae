"use client";

import { useCallback, useState } from "react";
import { useFieldsObj } from "@/features/dashboard/lib/useFieldObj";
import { gql, useMutation, type ApolloError } from "@apollo/client";
import {
  deserializeValue,
  useInvalidFields,
  useChangedFieldsAndDataForUpdate,
  makeDataGetter,
} from "@/features/dashboard/lib/dataGetter";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { GraphQLErrorNotice } from "@/features/dashboard/components/GraphQLErrorNotice";
import { CustomFields } from "../CustomFields";
import { MoreHorizontal, Pencil } from "lucide-react";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import Image from "next/image";
import { FieldContainer } from "@/components/ui/field-container";
import { FieldLabel } from "@/components/ui/field-label";
import { cn } from "@/lib/utils/cn";
import type { DataGetter } from "@/features/dashboard/lib/dataGetter";

const PlaceholderSVG = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="1200" height="1200" fill="none" className="w-full h-full">
    <rect width="1200" height="1200" fill="#EAEAEA" rx="3"/>
    <g opacity=".5">
      <g opacity=".5">
        <path fill="#FAFAFA" d="M600.709 736.5c-75.454 0-136.621-61.167-136.621-136.62 0-75.454 61.167-136.621 136.621-136.621 75.453 0 136.62 61.167 136.62 136.621 0 75.453-61.167 136.62-136.62 136.62Z"/>
        <path stroke="#C9C9C9" strokeWidth="2.418" d="M600.709 736.5c-75.454 0-136.621-61.167-136.621-136.62 0-75.454 61.167-136.621 136.621-136.621 75.453 0 136.62 61.167 136.62 136.621 0 75.453-61.167 136.62-136.62 136.62Z"/>
      </g>
      <path stroke="url(#a)" strokeWidth="2.418" d="M0-1.209h553.581" transform="scale(1 -1) rotate(45 1163.11 91.165)"/>
      <path stroke="url(#b)" strokeWidth="2.418" d="M404.846 598.671h391.726"/>
      <path stroke="url(#c)" strokeWidth="2.418" d="M599.5 795.742V404.017"/>
      <path stroke="url(#d)" strokeWidth="2.418" d="m795.717 796.597-391.441-391.44"/>
      <path fill="#fff" d="M600.709 656.704c-31.384 0-56.825-25.441-56.825-56.824 0-31.384 25.441-56.825 56.825-56.825 31.383 0 56.824 25.441 56.824 56.825 0 31.383-25.441 56.824-56.824 56.824Z"/>
      <g clipPath="url(#e)">
        <path fill="#666" fillRule="evenodd" d="M616.426 586.58h-31.434v16.176l3.553-3.554.531-.531h9.068l.074-.074 8.463-8.463h2.565l7.18 7.181V586.58Zm-15.715 14.654 3.698 3.699 1.283 1.282-2.565 2.565-1.282-1.283-5.2-5.199h-6.066l-5.514 5.514-.073.073v2.876a2.418 2.418 0 0 0 2.418 2.418h26.598a2.418 2.418 0 0 0 2.418-2.418v-8.317l-8.463-8.463-7.181 7.181-.071.072Zm-19.347 5.442v4.085a6.045 6.045 0 0 0 6.046 6.045h26.598a6.044 6.044 0 0 0 6.045-6.045v-7.108l1.356-1.355-1.282-1.283-.074-.073v-17.989h-38.689v23.43l-.146.146.146.147Z" clipRule="evenodd"/>
      </g>
      <path stroke="#C9C9C9" strokeWidth="2.418" d="M600.709 656.704c-31.384 0-56.825-25.441-56.825-56.824 0-31.384 25.441-56.825 56.825-56.825 31.383 0 56.824 25.441 56.824 56.825 0 31.383-25.441 56.824-56.824 56.824Z"/>
    </g>
    <defs>
      <linearGradient id="a" x1="554.061" x2="-.48" y1=".083" y2=".087" gradientUnits="userSpaceOnUse">
        <stop stopColor="#C9C9C9" stopOpacity="0"/>
        <stop offset=".208" stopColor="#C9C9C9"/>
        <stop offset=".792" stopColor="#C9C9C9"/>
        <stop offset="1" stopColor="#C9C9C9" stopOpacity="0"/>
      </linearGradient>
      <linearGradient id="b" x1="796.912" x2="404.507" y1="599.963" y2="599.965" gradientUnits="userSpaceOnUse">
        <stop stopColor="#C9C9C9" stopOpacity="0"/>
        <stop offset=".208" stopColor="#C9C9C9"/>
        <stop offset=".792" stopColor="#C9C9C9"/>
        <stop offset="1" stopColor="#C9C9C9" stopOpacity="0"/>
      </linearGradient>
      <linearGradient id="c" x1="600.792" x2="600.794" y1="404.677" y2="796.082" gradientUnits="userSpaceOnUse">
        <stop stopColor="#C9C9C9" stopOpacity="0"/>
        <stop offset=".208" stopColor="#C9C9C9"/>
        <stop offset=".792" stopColor="#C9C9C9"/>
        <stop offset="1" stopColor="#C9C9C9" stopOpacity="0"/>
      </linearGradient>
      <linearGradient id="d" x1="404.501" x2="796.232" y1="795.742" y2="404.011" gradientUnits="userSpaceOnUse">
        <stop stopColor="#C9C9C9" stopOpacity="0"/>
        <stop offset=".208" stopColor="#C9C9C9"/>
        <stop offset=".792" stopColor="#C9C9C9"/>
        <stop offset="1" stopColor="#C9C9C9" stopOpacity="0"/>
      </linearGradient>
      <clipPath id="e">
        <path fill="#fff" d="M577.297 575.839h46.907v46.907h-46.907z"/>
      </clipPath>
    </defs>
  </svg>
);

interface InlineEditProps {
  list: any;
  fields: string[];
  onSave: (itemGetter: DataGetter<any>) => void;
  selectedFields: string;
  itemGetter: DataGetter<any>;
  onCancel: () => void;
}

export function InlineEdit({
  list,
  fields,
  onSave,
  selectedFields,
  itemGetter,
  onCancel,
}: InlineEditProps) {
  const fieldsObj = useFieldsObj(list, fields);
  const { toast } = useToast();
  
  const [update, { loading: updateLoading, error: updateError }] = useMutation(
    gql`mutation update($id: ID!, $data: ${list.gqlNames.updateInputName}!) {
      item: ${list.gqlNames.updateMutationName}(where: { id: $id }, data: $data) {
        ${selectedFields}
      }
    }`,
    {
      errorPolicy: "all",
    }
  );
  
  const [state, setValue] = useState(() => {
    const value = deserializeValue(fieldsObj, itemGetter);
    return { value, item: itemGetter.data };
  });
  
  const { changedFields, dataForUpdate } = useChangedFieldsAndDataForUpdate(
    fieldsObj,
    itemGetter,
    state.value
  );
  
  const invalidFields = useInvalidFields(fieldsObj, state.value);
  
  const [forceValidation, setForceValidation] = useState(false);
  const [modalOpen, setModalOpen] = useState(false);
  const [mode, setMode] = useState<"view" | "edit">("view");
  
  const onReset = useCallback(() => {
    setValue(() => {
      const value = deserializeValue(fieldsObj, itemGetter);
      return { value, item: itemGetter.data };
    });
  }, [fieldsObj, itemGetter]);
  
  const hasChangedFields = !!changedFields.size;
  
  return (
    <>
      <div className="w-full h-full rounded-md relative overflow-hidden group border">
        <div className="relative w-full h-full">
          {mode === "edit" ? (
            <div className="p-4 space-y-4">
              <CustomFields
                fields={fieldsObj}
                onChange={useCallback(
                  (value: any) => {
                    setValue((state) => ({
                      item: state.item,
                      value: value(state.value),
                    }));
                  },
                  [setValue]
                )}
                forceValidation={forceValidation}
                invalidFields={invalidFields}
                value={state.value}
              />
              
              <div className="flex gap-2">
                <Button
                  size="sm"
                  disabled={updateLoading || !hasChangedFields || invalidFields.size > 0}
                  onClick={async () => {
                    const hasValidationErrors = invalidFields.size > 0;
                    if (hasValidationErrors) {
                      setForceValidation(true);
                      return;
                    }
                    
                    try {
                      const result = await update({
                        variables: {
                          data: dataForUpdate,
                          id: itemGetter.data.id,
                        },
                      });
                      
                      if (result.data?.item) {
                        const itemGetter = makeDataGetter(
                          result.data,
                          result.errors
                        ).get("item");
                        onSave(itemGetter);
                        setMode("view");
                        toast({
                          title: "Success",
                          description: `Updated ${list.singular}`,
                        });
                      }
                    } catch (err) {
                      // Error is handled by updateError
                    }
                  }}
                >
                  Save
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => {
                    onReset();
                    setMode("view");
                  }}
                >
                  Cancel
                </Button>
              </div>
              
              {updateError && (
                <GraphQLErrorNotice
                  errors={updateError?.graphQLErrors}
                  networkError={updateError?.networkError}
                />
              )}
            </div>
          ) : (
            <>
              {/* View Mode - Display the first field value or image */}
              {(() => {
                const firstFieldKey = fields[0];
                const firstField = fieldsObj[firstFieldKey];
                const fieldValue = state.value[firstFieldKey];
                
                if (!firstField || !fieldValue) return <PlaceholderSVG />;
                
                // If it's an image field
                if (firstField.controller.fieldType === "image" && fieldValue.kind === "value" && fieldValue.value) {
                  const imageData = fieldValue.value as any;
                  if (imageData.data?.src) {
                    return (
                      <Image
                        src={imageData.data.src}
                        alt={firstField.label}
                        fill
                        className="object-cover"
                      />
                    );
                  }
                }
                
                // If it's a relationship field with an image
                if (firstField.controller.fieldType === "relationship" && fieldValue.kind === "value") {
                  const relData = fieldValue.value as any;
                  if (relData && relData.value?.image?.url) {
                    return (
                      <Image
                        src={relData.value.image.url}
                        alt={firstField.label}
                        fill
                        className="object-cover"
                      />
                    );
                  }
                }
                
                // Default placeholder
                return <PlaceholderSVG />;
              })()}
              
              {/* Hover overlay */}
              <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                <Button
                  size="sm"
                  variant="secondary"
                  onClick={() => setMode("edit")}
                >
                  <Pencil className="w-4 h-4 mr-2" />
                  Edit
                </Button>
              </div>
            </>
          )}
        </div>
        
        {/* Field labels at bottom */}
        {mode === "view" && (
          <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-3">
            <div className="space-y-1">
              {fields.map((fieldKey) => {
                const field = fieldsObj[fieldKey];
                const fieldValue = state.value[fieldKey];
                if (!field || !fieldValue || fieldValue.kind !== "value") return null;
                
                return (
                  <div key={fieldKey} className="text-white text-xs">
                    <span className="font-medium">{field.label}:</span>{" "}
                    <span className="opacity-90">
                      {(() => {
                        const val = fieldValue.value;
                        if (typeof val === "string") return val;
                        if (typeof val === "number") return val.toString();
                        if (val && typeof val === "object") {
                          // Handle relationship values
                          if ("label" in val) return val.label;
                          if ("value" in val && val.value?.label) return val.value.label;
                        }
                        return "—";
                      })()}
                    </span>
                  </div>
                );
              })}
            </div>
          </div>
        )}
      </div>
      
      {/* Modal for detailed editing */}
      <Dialog open={modalOpen} onOpenChange={setModalOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit {list.singular}</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <CustomFields
              fields={fieldsObj}
              onChange={useCallback(
                (value: any) => {
                  setValue((state) => ({
                    item: state.item,
                    value: value(state.value),
                  }));
                },
                [setValue]
              )}
              forceValidation={forceValidation}
              invalidFields={invalidFields}
              value={state.value}
            />
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}