"use client";

import { Apollo<PERSON><PERSON>, InM<PERSON>oryCache, ApolloProvider as Apollo<PERSON>roviderBase } from '@apollo/client';
import { useMemo } from 'react';

const createApolloClient = () => {
  return new ApolloClient({
    uri: '/api/graphql',
    cache: new InMemoryCache(),
    defaultOptions: {
      watchQuery: {
        fetchPolicy: 'cache-and-network',
      },
    },
  });
};

export function ApolloProvider({ children }: { children: React.ReactNode }) {
  const client = useMemo(() => createApolloClient(), []);
  
  return (
    <ApolloProviderBase client={client}>
      {children}
    </ApolloProviderBase>
  );
}