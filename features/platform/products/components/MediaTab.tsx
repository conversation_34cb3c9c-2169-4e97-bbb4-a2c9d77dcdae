
"use client";

import { CustomFields } from "./CustomFields";
import * as RelationshipView from "./views/Relationship";
import type { FieldMeta } from "@/features/dashboard/types";
import type { Value } from "@/features/dashboard/lib/useChangedFieldsAndDataForUpdate";
import type { GraphQLError } from 'graphql';

// This matches the expected FieldValue type from Fields component
interface FieldValue {
  kind: 'error' | 'value';
  errors?: Array<{ message: string }>;
  value?: unknown;
}

interface MediaTabProps {
  fields: Record<string, FieldMeta>;
  value: Value;
  onChange?: (valueUpdater: (prevValue: Value) => Value) => void;
  forceValidation: boolean;
  invalidFields: ReadonlySet<string>;
}

export function MediaTab({
  fields,
  value,
  onChange,
  forceValidation,
  invalidFields,
}: MediaTabProps) {
  // Define custom views for specific fields by path
  const fieldViews = {
    productImages: RelationshipView,
  };

  // Convert Value to the format expected by CustomFields
  const fieldsValue = Object.keys(fields).reduce((acc, fieldKey) => {
    const val = value[fieldKey];
    if (val) {
      if (val.kind === 'error') {
        // Convert GraphQLError to simple error format
        acc[fieldKey] = {
          kind: 'error',
          errors: val.errors.map(err => ({ message: err.message }))
        };
      } else {
        acc[fieldKey] = val;
      }
    }
    return acc;
  }, {} as Record<string, FieldValue>);

  // Convert onChange to the format expected by CustomFields
  const handleChange = onChange ? (updater: (prevVal: Record<string, FieldValue>) => Record<string, FieldValue>) => {
    onChange((prevValue) => {
      const newFieldsValue = updater(fieldsValue);
      // Convert back to Value format
      const updates: Value = {};
      Object.keys(newFieldsValue).forEach((key) => {
        const fieldVal = newFieldsValue[key];
        if (fieldVal.kind === 'error' && fieldVal.errors) {
          // Convert simple errors back to GraphQLError format
          // Since we can't create real GraphQLErrors, we'll keep the value type
          updates[key] = fieldVal as any;
        } else {
          updates[key] = fieldVal;
        }
      });
      return {
        ...prevValue,
        ...updates,
      };
    });
  } : undefined;

  return (
    <CustomFields
      fields={fields}
      value={fieldsValue}
      onChange={handleChange}
      forceValidation={forceValidation}
      invalidFields={invalidFields}
      fieldViews={fieldViews}
    />
  );
}